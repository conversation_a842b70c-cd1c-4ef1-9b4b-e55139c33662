import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  HealthAndSafety,
  NetworkCheck,
  CheckCircle,
  Error,
  Warning,
} from '@mui/icons-material';
import StatusIndicator, { type StatusType } from '../common/StatusIndicator';
import { useRealTimeHealth } from '../../hooks/useApi';
import { useRealTimeHealthUpdates } from '../../hooks/useWebSocket';

interface ServiceStatus {
  name: string;
  status: StatusType;
  lastCheck: string;
  details?: string;
}

const SystemHealthMonitor: React.FC = () => {
  const { data: health, loading, error } = useRealTimeHealth(15000);
  const [services, setServices] = useState<ServiceStatus[]>([
    { name: 'API Service', status: 'unknown', lastCheck: 'Never' },
    { name: 'Database', status: 'unknown', lastCheck: 'Never' },
    { name: 'Message Queue', status: 'unknown', lastCheck: 'Never' },
    { name: 'Workers', status: 'unknown', lastCheck: 'Never' },
  ]);

  // Real-time health updates
  useRealTimeHealthUpdates(healthUpdate => {
    console.log('Received health update:', healthUpdate);
    updateServiceStatuses(healthUpdate);
  });

  // Update service statuses based on health data
  const updateServiceStatuses = (healthData: any) => {
    const now = new Date().toLocaleTimeString();

    setServices(prev =>
      prev.map(service => {
        switch (service.name) {
          case 'API Service':
            return {
              ...service,
              status: healthData?.status === 'healthy' ? 'healthy' : 'unhealthy',
              lastCheck: now,
              details: `Version: ${healthData?.version || 'unknown'}`,
            };
          case 'Database':
            return {
              ...service,
              status: healthData?.database === 'healthy' ? 'healthy' : 'unhealthy',
              lastCheck: now,
              details: 'PostgreSQL connection',
            };
          default:
            return service;
        }
      })
    );
  };

  useEffect(() => {
    if (health) {
      updateServiceStatuses(health);
    }
  }, [health]);

  const getOverallStatus = (): StatusType => {
    if (loading) return 'loading';
    if (error) return 'unhealthy';

    const unhealthyServices = services.filter(s => s.status === 'unhealthy');
    const warningServices = services.filter(s => s.status === 'warning');

    if (unhealthyServices.length > 0) return 'unhealthy';
    if (warningServices.length > 0) return 'warning';
    if (services.some(s => s.status === 'healthy')) return 'healthy';

    return 'unknown';
  };

  const getStatusIcon = (status: StatusType) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle color="success" />;
      case 'unhealthy':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'loading':
        return <CircularProgress size={20} />;
      default:
        return <NetworkCheck color="disabled" />;
    }
  };

  if (error) {
    return <Alert severity="error">Unable to fetch system health: {error}</Alert>;
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
          <HealthAndSafety color="primary" />
          <Typography variant="h6">System Health Monitor</Typography>
          <Box sx={{ ml: 'auto' }}>
            <StatusIndicator
              status={getOverallStatus()}
              label="Overall"
              description="Overall system health status"
              variant="chip"
              animate={loading}
            />
          </Box>
        </Box>

        {/* Service Status List */}
        <List dense>
          {services.map((service, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemIcon sx={{ minWidth: 40 }}>{getStatusIcon(service.status)}</ListItemIcon>
              <ListItemText
                primary={service.name}
                secondary={
                  <Box>
                    <Typography variant="caption" color="textSecondary">
                      Last check: {service.lastCheck}
                    </Typography>
                    {service.details && (
                      <Typography variant="caption" display="block" color="textSecondary">
                        {service.details}
                      </Typography>
                    )}
                  </Box>
                }
              />
              <StatusIndicator
                status={service.status}
                label=""
                variant="dot"
                showLabel={false}
                animate={service.status === 'loading'}
              />
            </ListItem>
          ))}
        </List>

        {/* Health Metrics */}
        {health && (
          <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
            <Typography variant="subtitle2" gutterBottom>
              Health Details
            </Typography>
            <Grid container spacing={2}>
              <Grid>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="textSecondary">
                    Service
                  </Typography>
                  <Chip label={health.service} size="small" variant="outlined" color="primary" />
                </Box>
              </Grid>
              <Grid>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="textSecondary">
                    Version
                  </Typography>
                  <Chip label={health.version} size="small" variant="outlined" color="secondary" />
                </Box>
              </Grid>
            </Grid>

            <Typography
              variant="caption"
              color="textSecondary"
              display="block"
              sx={{ mt: 1, textAlign: 'center' }}
            >
              Last updated: {new Date(health.timestamp).toLocaleString()}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SystemHealthMonitor;
